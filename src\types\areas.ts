export interface GetAreasResponse {
  status_code: number;
  status: string;
  message: null;
  data: Area[];
}

export interface Area {
  id: number;
  area_group_id: string;
  area_group: string;
  name: string;
  code: string;
  type: string;
  radius: string;
  color: string;
  is_personal: string;
}

export interface CreateAreaFormData {
  name: string;
  code: string;
  type: number;
  color: string;
  is_personal: boolean;
  area_group_id: number;
  points:
    | {
        lat: number;
        lng: number;
      }[]
    | {
        center: {
          lat: number;
          lng: number;
        };
        radius: number;
      };
}
export interface CreateAreaResponse {
  status_code: number;
  status: number;
  message: string;
  data: {
    name: string;
    area_group_id: number;
    code: string;
    type: number;
    color: string;
    is_personal: boolean;
    user_id: number;
    updated_at: Date;
    created_at: Date;
    id: number;
  };
}
