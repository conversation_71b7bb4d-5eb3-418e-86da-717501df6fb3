import type {
  Area,
  CreateAreaFormData,
  CreateAreaResponse,
  GetAreasResponse,
} from "@/types/areas";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface AreasAtom {
  areas: Area[];
  isLoading: boolean;
  error: string;
}

interface AreasAtomAction {
  getAreas: () => void;
  createArea: (formData: CreateAreaFormData, callback?: () => void) => void;
}

export const areasAtom = atom<AreasAtom, AreasAtomAction>({
  key: "areas-atom",
  default: {
    areas: [],
    isLoading: true,
    error: "",
  },

  actions: {
    async getAreas() {
      areasAtom.change("isLoading", true);
      try {
        const { data } = await endpoint.get<GetAreasResponse>("areas");
        areasAtom.change("areas", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
          areasAtom.change("error", error.response?.data.message);
        } else {
          toast.error((error as Error).message);
          areasAtom.change("error", (error as Error).message);
        }
      } finally {
        areasAtom.change("isLoading", false);
      }
    },

    async createArea(formData: CreateAreaFormData, callback?: () => void) {
      try {
        areasAtom.change("isLoading", true);

        const { data } = await endpoint.post<CreateAreaResponse>(
          "area2s",
          formData,
        );

        toast.success(data.message);

        callback?.();
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
        } else {
          toast.error((error as Error).message);
        }
      } finally {
        areasAtom.change("isLoading", false);
      }
    },
  },
});
