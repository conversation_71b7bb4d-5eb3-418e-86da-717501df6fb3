import { areasAtom } from "@/atoms/areas/areas-atom";
import { DataTable } from "@/components/table/data-table";
import { DataTableColumnHeader } from "@/components/table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { OverlayLoader } from "@/components/utils/overlay-loader";
import { useIsMobile } from "@/hooks/use-mobile";
import type { Area } from "@/types/areas";
import { URLS } from "@/utils/urls";
import type { ColumnDef } from "@tanstack/react-table";
import type { LatLngExpression } from "leaflet";
import {
  Edit,
  Locate,
  MessageCircle,
  Plus,
  RefreshCcw,
  Trash2,
  Wifi,
} from "lucide-react";
import { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import { Link } from "react-router";

const columns: ColumnDef<Area>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    accessorKey: "name",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Name" />;
    },
    cell: ({ row }) => {
      const name = row.original.name;
      return <div>{name}</div>;
    },
  },
  {
    accessorKey: "type",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Type" />;
    },
    cell: ({ row }) => {
      const type = row.original.type;
      return <div>{type}</div>;
    },
  },
  {
    accessorKey: "area_group",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Area Group" />;
    },
    cell: ({ row }) => {
      return <div>{row.original.area_group}</div>;
    },
  },
  {
    accessorKey: "code",
    header: ({ column }) => {
      return <DataTableColumnHeader column={column} title="Code" />;
    },
    cell: ({ row }) => {
      return <div>{row.original.code}</div>;
    },
  },
  {
    id: "actions",
    header: () => <div className="text-start">Actions</div>,
    cell: () => {
      return (
        <div className="flex gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <MessageCircle />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Message</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Locate />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Track</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Wifi />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Data Accuracy</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Edit />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Edit</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="secondary" size="icon" className="size-7">
                <Trash2 color="red" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Delete</p>
            </TooltipContent>
          </Tooltip>
        </div>
      );
    },
  },
];

export default function AreasPage() {
  const isMobile = useIsMobile();

  const [tableSize, setTableSize] = useState(
    localStorage.getItem("gps-table-size")
      ? JSON.parse(localStorage.getItem("gps-table-size")!)
      : 50,
  );

  const handleResize = (e: number) => {
    setTableSize(e);
    localStorage.setItem("gps-table-size", JSON.stringify(e));
  };

  useEffect(() => {
    areasAtom.getAreas();
  }, []);

  const { areas, isLoading } = areasAtom.useValue();

  if (isLoading) {
    return <OverlayLoader />;
  }

  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <div className="flex flex-col gap-3 p-2">
          <AreasHeading />
          <DataTable columns={columns} data={areas} />
        </div>
        <div className="h-[500px] w-full p-2">
          <AreasMap />
        </div>
      </div>
    );
  }

  return (
    <ResizablePanelGroup direction="horizontal">
      <ResizablePanel
        className="flex flex-col gap-3 p-4"
        defaultSize={tableSize}
        onResize={handleResize}
        minSize={30}
      >
        <AreasHeading />
        <DataTable columns={columns} data={areas} />
      </ResizablePanel>
      <ResizableHandle withHandle />
      <ResizablePanel className="p-4">
        <AreasMap />
      </ResizablePanel>
    </ResizablePanelGroup>
  );
}

const AreasHeading = () => {
  return (
    <div className="flex items-center justify-between">
      <h1 className="text-primary text-2xl font-bold">Areas</h1>
      <div className="flex gap-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="secondary" size="icon" className="size-7">
              <RefreshCcw />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Refresh</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Link to={URLS.addArea}>
              <Button variant="secondary" size="icon" className="size-7">
                <Plus />
              </Button>
            </Link>
          </TooltipTrigger>
          <TooltipContent>
            <p>Add Area</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </div>
  );
};

const ResizeHandler = () => {
  const map = useMap();
  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 100); // slight delay so container finishes rendering
  }, [map]);
  return null;
};

const AreasMap = () => {
  const position: LatLngExpression = [26.8206, 30.8025];

  return (
    <MapContainer
      className="z-20 h-full w-full"
      center={position}
      zoom={6}
      scrollWheelZoom={false}
    >
      <ResizeHandler />
      <TileLayer
        className="h-full w-full"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      <Marker position={position}>
        <Popup>
          A pretty CSS3 popup. <br /> Easily customizable.
        </Popup>
      </Marker>
    </MapContainer>
  );
};
