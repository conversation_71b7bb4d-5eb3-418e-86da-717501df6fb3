import type { AreaGroup, GetAreaGroupsResponse } from "@/types/area-groups";
import { endpoint } from "@/utils/endpoints";
import { atom } from "@mongez/react-atom";
import { AxiosError } from "axios";
import toast from "react-hot-toast";

interface AreaGroupsAtom {
  areaGroups: AreaGroup[];
  isLoading: boolean;
  error: string;
}

interface AreaGroupsAtomAction {
  getAreaGroups: () => void;
}

export const areaGroupsAtom = atom<AreaGroupsAtom, AreaGroupsAtomAction>({
  key: "areas-atom",
  default: {
    areaGroups: [],
    isLoading: true,
    error: "",
  },

  actions: {
    async getAreaGroups() {
      areaGroupsAtom.change("isLoading", true);
      try {
        const { data } =
          await endpoint.get<GetAreaGroupsResponse>("area-groups");
        areaGroupsAtom.change("areaGroups", data.data);
      } catch (error) {
        if (error instanceof AxiosError) {
          toast.error(error.response?.data.message);
          areaGroupsAtom.change("error", error.response?.data.message);
        } else {
          toast.error((error as Error).message);
          areaGroupsAtom.change("error", (error as Error).message);
        }
      } finally {
        areaGroupsAtom.change("isLoading", false);
      }
    },
  },
});
