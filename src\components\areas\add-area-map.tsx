import { areaShapePointsAtom } from "@/atoms/areas/area-shape-points-atom";
import { selectedDrawerTypeAtom } from "@/atoms/areas/selected-drawer-type-atom";
import type { LatLngExpression } from "leaflet";
import { useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, TileLayer, useMap } from "react-leaflet";
import { CircleDrawer } from "../utils/circle-drawer";
import { PolygonDrawer } from "../utils/polygon-drawer";

const ResizeHandler = () => {
  const map = useMap();
  useEffect(() => {
    setTimeout(() => {
      map.invalidateSize();
    }, 100); // slight delay so container finishes rendering
  }, [map]);
  return null;
};

export const AddAreaMap = () => {
  const position: LatLngExpression = [26.8206, 30.8025];
  const { selectedType } = selectedDrawerTypeAtom.useValue();

  return (
    <MapContainer
      className="z-20 h-full w-full"
      center={position}
      zoom={6}
      scrollWheelZoom={false}
    >
      <ResizeHandler />
      <TileLayer
        className="h-full w-full"
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />

      {selectedType === 1 && (
        <CircleDrawer
          color="red"
          allowMultiple={true}
          onCircleComplete={(circle) => {
            areaShapePointsAtom.change("value", circle);
          }}
        />
      )}

      {selectedType === 2 && (
        <PolygonDrawer
          color="purple"
          allowMultiple={true}
          onPolygonComplete={(points) => {
            areaShapePointsAtom.change("value", points);
          }}
        />
      )}

      {/* <Marker position={position}>
        <Popup>
          A pretty CSS3 popup. <br /> Easily customizable.
        </Popup>
      </Marker> */}
    </MapContainer>
  );
};
